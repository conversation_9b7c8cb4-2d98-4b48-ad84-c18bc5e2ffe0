<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>آفاق الشباب | نعمل من أجل مستقبل أفضل</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Cairo -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700;900&display=swap" rel="stylesheet">
    
    <style>
        /* استخدام خط Cairo للمحتوى العربي */
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #10062E;
        }
        .hero-bg-image {
            background-image: url('https://images.unsplash.com/photo-1531297484001-80022131f5a1?q=80&w=1920&auto=format&fit=crop');
            background-size: cover;
            background-position: center;
        }
        .full-width-bg-image {
            background-image: url('https://images.unsplash.com/photo-1519389950473-47ba0277781c?q=80&w=1920&auto=format&fit=crop');
            background-size: cover;
            background-position: center;
            background-attachment: fixed; /* تأثير البارالاكس */
        }
        /* تدرج لوني للنصوص مستوحى من الشعار */
        .brand-gradient-text {
            background: linear-gradient(90deg, #F43F8A, #4A90E2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        /* لإخفاء العناصر بشكل افتراضي */
        .hidden-page { display: none; }
        /* للتحكم في دوران أيقونة الأكورديون */
        .accordion-button[aria-expanded="true"] .accordion-icon {
            transform: rotate(180deg);
        }
        .loading-container, .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .content-container {
            display: none; /* إخفاء المحتوى الافتراضي حتى يتم التحقق */
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body class="bg-[#10062E] text-gray-200">

    <!-- شاشة التحميل (تظهر أولاً) -->
    <div id="loading-page" class="loading-container text-white">
        <div class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-purple-500"></div>
        <p class="ml-4 text-lg">جارِ التحقق...</p>
    </div>

    <!-- شاشة تسجيل الدخول (تظهر إذا كان المستخدم غير مسجل أو من جهاز جديد) -->
    <div id="login-page" class="login-container hidden">
        <div class="w-full max-w-md bg-[#140A38] p-8 rounded-lg shadow-2xl shadow-purple-500/10">
            <h1 class="text-3xl font-bold text-center brand-gradient-text mb-6">تسجيل الدخول الآمن</h1>
            <p class="text-center text-gray-400 mb-6">يرجى تسجيل الدخول للوصول إلى المحتوى.</p>
            
            <div id="message-feedback" class="hidden px-4 py-3 rounded-lg relative mb-6 text-center" role="alert">
                <strong class="font-bold"></strong>
                <span class="block sm:inline"></span>
            </div>
            
            <form id="login-form">
                <div class="mb-4">
                    <label for="email" class="block text-gray-300 mb-2 font-semibold">البريد الإلكتروني</label>
                    <input type="email" id="email" required class="w-full p-3 bg-[#10062E] border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-pink-500 focus:outline-none">
                </div>
                <div class="mb-6">
                    <label for="password" class="block text-gray-300 mb-2 font-semibold">كلمة المرور</label>
                    <input type="password" id="password" required class="w-full p-3 bg-[#10062E] border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-pink-500 focus:outline-none">
                </div>
                <button type="submit" id="login-button" class="w-full bg-[#F43F8A] text-white font-bold py-3 px-6 rounded-full hover:bg-pink-500 transition duration-300">تسجيل الدخول</button>
            </form>
        </div>
    </div>
    
    <!-- محتوى الصفحة الرئيسية (مخفي افتراضيًا) -->
    <div id="content-page" class="content-container">
        <!-- الشريط العلوي -->
        <header class="bg-[#10062E]/80 backdrop-blur-sm shadow-lg shadow-purple-500/10 sticky top-0 z-50">
            <div class="container mx-auto px-6 py-3 flex justify-between items-center">
                <a href="#/" class="flex items-center gap-2">
                    <img src="logos.png" alt="شعار آفاق الشباب" class="h-16 w-auto drop-shadow-lg rounded-full" style="max-width:180px;">
                </a>
                <nav class="hidden md:flex items-center space-x-8 space-x-reverse">
                    <a href="#/" class="text-gray-300 hover:text-white transition duration-300">الرئيسية</a>
                    <a href="#/about" class="text-gray-300 hover:text-white transition duration-300">من نحن</a>
                    <a href="#/partner" class="text-gray-300 hover:text-white transition duration-300">كن شريكاً</a>
                    <a href="#/contact" class="text-gray-300 hover:text-white transition duration-300">تواصل معنا</a>
                </nav>
                <button id="logout-button" class="bg-[#F43F8A] text-white font-bold py-2 px-6 rounded-full hover:bg-pink-500 transition duration-300 shadow-md shadow-[#F43F8A]/30">
                    تسجيل الخروج
                </button>
            </div>
        </header>

        <!-- حاوية الصفحات (تم دمجها هنا) -->
        <div id="router-outlet">
            <!-- ======== الصفحة الرئيسية ======== -->
            <main id="home-page">
                <section class="hero-bg-image text-white h-[85vh] flex items-center justify-center">
                    <div class="bg-black bg-opacity-50 w-full h-full flex flex-col items-center justify-center text-center p-6">
                        <h1 class="text-4xl md:text-6xl font-extrabold mb-4 leading-tight">نصنع قادة الغد اليوم</h1>
                        <p class="text-lg md:text-xl max-w-2xl mb-8 text-gray-300">نحن نؤمن بقدرات الشباب ونعمل على تزويدهم بالمهارات والمعرفة اللازمة لتحقيق طموحاتهم وبناء مستقبل مشرق.</p>
                        <a href="#" class="bg-[#F43F8A] text-white font-bold py-3 px-8 rounded-full hover:bg-pink-500 transition duration-300 text-lg shadow-lg shadow-[#F43F8A]/40">انضم إلينا</a>
                    </div>
                </section>
                <section id="problem" class="py-20 bg-[#140A38]">
                    <div class="container mx-auto px-6 text-center">
                        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">التحدي الذي يواجه شبابنا</h2>
                        <p class="max-w-3xl mx-auto text-gray-400 text-lg mb-12">يواجه الشباب اليوم تحديات متزايدة، من الفجوة بين التعليم وسوق العمل إلى صعوبة الوصول إلى الموارد اللازمة لتطوير مهاراتهم.</p>
                        <div class="flex flex-col md:flex-row justify-center items-center gap-8">
                            <img src="https://images.unsplash.com/photo-1549923746-c502d488b3ea?q=80&w=600&auto=format&fit=crop" alt="شباب يواجهون تحديات" class="rounded-lg shadow-xl w-full md:w-1/2">
                            <div class="text-right md:w-1/2"><h3 class="text-2xl font-bold mb-3 brand-gradient-text">لماذا دعم الشباب؟</h3><p class="text-gray-300 leading-relaxed">الاستثمار في الشباب هو استثمار في المستقبل. عندما نمكّن شاباً، نحن نطلق طاقة إيجابية تعود بالنفع على المجتمع بأكمله.</p></div>
                        </div>
                    </div>
                </section>
                <section id="solution" class="full-width-bg-image h-96 flex items-center justify-center text-white text-center p-6">
                    <div class="bg-black bg-opacity-60 w-full h-full flex flex-col items-center justify-center"><h2 class="text-4xl font-bold mb-4">نحن نستثمر في طاقات الشباب</h2><p class="max-w-2xl text-lg">نقدم برامج تدريبية وورش عمل متخصصة لسد الفجوة بين المهارات الأكاديمية ومتطلبات سوق العمل الحديث.</p></div>
                </section>
                <section id="impact" class="py-20 bg-[#10062E]">
                    <div class="container mx-auto px-6 text-center"><h2 class="text-3xl md:text-4xl font-bold text-white mb-12">تأثير دعمكم بالأرقام</h2><div class="grid grid-cols-1 md:grid-cols-3 gap-12"><div class="p-6"><span class="text-5xl font-extrabold brand-gradient-text">50+</span><h3 class="text-xl font-bold mt-4 text-white">برنامج تدريبي</h3></div><div class="p-6"><span class="text-5xl font-extrabold brand-gradient-text">4,000+</span><h3 class="text-xl font-bold mt-4 text-white">شاب تم تدريبه</h3></div><div class="p-6"><span class="text-5xl font-extrabold brand-gradient-text">200+</span><h3 class="text-xl font-bold mt-4 text-white">شراكة مع شركات</h3></div></div></div>
                </section>
                <section id="stories" class="py-20 bg-[#140A38]">
                    <div class="container mx-auto px-6"><h2 class="text-3xl md:text-4xl font-bold text-white mb-12 text-center">قصص نجاح ملهمة</h2><div class="flex flex-col lg:flex-row items-center bg-[#10062E] rounded-lg shadow-2xl shadow-purple-500/10 overflow-hidden"><img class="w-full lg:w-1/2 h-full object-cover" src="https://images.unsplash.com/photo-1556761175-b413da4baf72?q=80&w=800&auto=format&fit=crop" alt="شاب ناجح"><div class="p-8 md:p-12 w-full lg:w-1/2 text-right"><p class="font-bold mb-2 text-[#F43F8A]">قصة علي</p><h3 id="story-title" class="text-3xl font-bold mb-4 text-white">"البرنامج التدريبي منحني الثقة والمهارة لبدء مشروعي الخاص."</h3><p id="story-description" class="text-gray-400 leading-relaxed mb-6">كان علي يمتلك فكرة رائعة ولكنه كان يفتقر إلى المهارات الإدارية. بعد انضمامه لبرنامج ريادة الأعمال لدينا، تمكن من تحويل فكرته إلى شركة ناشئة ناجحة.</p><a href="#" class="font-bold text-[#4A90E2] hover:text-blue-400 transition duration-300">اقرأ المزيد من القصص ←</a></div></div></div>
                </section>
                <section class="bg-gradient-to-r from-[#F43F8A] to-[#4A90E2] text-white py-20">
                    <div class="container mx-auto px-6 text-center"><h2 class="text-3xl md:text-4xl font-bold mb-4">هل أنت مستعد لدعم المستقبل؟</h2><p class="text-lg max-w-2xl mx-auto mb-8 text-gray-200">كل مساهمة، سواء بالوقت أو الدعم، تساعدنا على الوصول إلى المزيد من الشباب وتزويدهم بالأدوات التي يحتاجونها للنجاح.</p><a href="#" class="bg-white text-[#10062E] font-bold py-3 px-8 rounded-full hover:bg-gray-200 transition duration-300 text-lg shadow-2xl">انضم إلينا</a></div>
                </section>
            </main>
    
            <!-- ======== صفحة "من نحن" ======== -->
            <main id="about-page" class="hidden-page">
                <div class="bg-[#140A38] py-20">
                    <div class="container mx-auto px-6 text-center">
                        <h1 class="text-4xl md:text-5xl font-extrabold brand-gradient-text mb-4">من نحن</h1>
                        <p class="text-lg md:text-xl max-w-3xl mx-auto text-gray-300">نحن "آفاق الشباب"، منظمة غير ربحية تأسست في ليبيا، نؤمن بأن الشباب هم حجر الزاوية لمستقبل مشرق ومزدهر.</p>
                    </div>
                </div>
                <div class="py-20 bg-[#10062E]">
                    <div class="container mx-auto px-6 grid md:grid-cols-2 gap-12 items-center">
                        <div class="text-right">
                            <h2 class="text-3xl font-bold text-white mb-4">رؤيتنا ورسالتنا</h2>
                            <p class="text-gray-400 mb-4 leading-loose"><strong>رؤيتنا:</strong> أن نكون المنصة الرائدة في تمكين الشباب الليبي، وخلق جيل من القادة والمبتكرين القادرين على مواجهة تحديات المستقبل.</p>
                            <p class="text-gray-400 leading-loose"><strong>رسالتنا:</strong> تصميم وتنفيذ برامج ومبادرات نوعية تهدف إلى تطوير المهارات، تعزيز المعرفة، وتوفير الفرص للشباب، بالتعاون مع شركاء محليين ودوليين.</p>
                        </div>
                        <div>
                            <img src="https://images.unsplash.com/photo-1521737711867-e3b97375f902?q=80&w=800&auto=format&fit=crop" class="rounded-lg shadow-2xl shadow-purple-500/20" alt="فريق عمل آفاق الشباب">
                        </div>
                    </div>
                </div>
                <div id="faq" class="py-20 bg-[#140A38]">
                    <div class="container mx-auto px-6">
                        <h2 class="text-3xl md:text-4xl font-bold text-white mb-12 text-center">الأسئلة الشائعة</h2>
                        <div class="max-w-3xl mx-auto space-y-4">
                            <div class="bg-[#10062E] rounded-lg">
                                <h2><button class="accordion-button flex justify-between items-center w-full p-6 text-right font-semibold text-white" aria-expanded="false"><span class="text-lg">ما هي الفئات التي تستهدفها "آفاق الشباب"؟</span><svg class="accordion-icon w-6 h-6 text-gray-400 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button></h2>
                                <div class="accordion-content hidden"><p class="p-6 pt-0 text-gray-400">نستهدف الشباب في الفئة العمرية من 16 إلى 30 عامًا في جميع أنحاء ليبيا، مع التركيز على طلاب الجامعات، الخريجين الجدد، والباحثين عن عمل.</p></div>
                            </div>
                            <div class="bg-[#10062E] rounded-lg">
                                 <h2><button class="accordion-button flex justify-between items-center w-full p-6 text-right font-semibold text-white" aria-expanded="false"><span class="text-lg">كيف يمكنني الانضمام أو التطوع؟</span><svg class="accordion-icon w-6 h-6 text-gray-400 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button></h2>
                                <div class="accordion-content hidden"><p class="p-6 pt-0 text-gray-400">يمكنك متابعة صفحاتنا على وسائل التواصل الاجتماعي للاطلاع على آخر البرامج وفرص التطوع المتاحة. زر "انضم إلينا" في الأعلى هو نقطة البداية!</p></div>
                            </div>
                            <div class="bg-[#10062E] rounded-lg">
                                 <h2><button class="accordion-button flex justify-between items-center w-full p-6 text-right font-semibold text-white" aria-expanded="false"><span class="text-lg">هل برامجكم مجانية؟</span><svg class="accordion-icon w-6 h-6 text-gray-400 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button></h2>
                                <div class="accordion-content hidden"><p class="p-6 pt-0 text-gray-400">نعم، معظم برامجنا وورش العمل تقدم بشكل مجاني بالكامل بفضل دعم شركائنا والمانحين. بعض البرامج المتقدمة قد تتطلب رسومًا رمزية لضمان الالتزام.</p></div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
    
            <!-- ======== صفحة "كن شريكاً" ======== -->
            <main id="partner-page" class="hidden-page">
                <div class="bg-[#140A38] py-20">
                    <div class="container mx-auto px-6 text-center">
                        <h1 class="text-4xl md:text-5xl font-extrabold brand-gradient-text mb-4">كن شريكاً في النجاح</h1>
                        <p class="text-lg md:text-xl max-w-3xl mx-auto text-gray-300">نؤمن بأن الشراكات الفعالة هي مفتاح تحقيق تأثير مستدام. انضم إلينا لنصنع معاً مستقبلاً أفضل للشباب الليبي.</p>
                    </div>
                </div>
                <div class="py-20 bg-[#10062E]">
                    <div class="container mx-auto px-6">
                        <h2 class="text-3xl font-bold text-white mb-12 text-center">لماذا الشراكة مع آفاق الشباب؟</h2>
                        <div class="grid md:grid-cols-3 gap-8 text-center">
                            <div class="bg-[#140A38] p-8 rounded-lg shadow-lg shadow-purple-500/10"><h3 class="text-xl font-bold text-white mb-3">تأثير اجتماعي ملموس</h3><p class="text-gray-400">ساهم مباشرة في تطوير مهارات الشباب وزيادة فرصهم في سوق العمل.</p></div>
                            <div class="bg-[#140A38] p-8 rounded-lg shadow-lg shadow-purple-500/10"><h3 class="text-xl font-bold text-white mb-3">تعزيز العلامة التجارية</h3><p class="text-gray-400">اربط علامتك التجارية بقضية نبيلة واظهر التزامك بالمسؤولية الاجتماعية.</p></div>
                            <div class="bg-[#140A38] p-8 rounded-lg shadow-lg shadow-purple-500/10"><h3 class="text-xl font-bold text-white mb-3">الوصول إلى المواهب</h3><p class="text-gray-400">كن أول من يكتشف المواهب الشابة والواعدة في مختلف المجالات.</p></div>
                        </div>
                    </div>
                </div>
                 <div class="py-20 bg-[#140A38]">
                    <div class="container mx-auto px-6 grid md:grid-cols-2 gap-12 items-center">
                        <div>
                            <img src="business-unity.jpg" class="rounded-lg shadow-2xl shadow-purple-500/20 object-cover" alt="شركة عمل" style="max-width:420px; max-height:260px; width:100%; height:auto;">
                        </div>
                        <div class="text-right">
                            <h2 class="text-3xl font-bold text-white mb-4">فرص الشراكة المتاحة</h2>
                            <ul class="space-y-4 text-gray-300">
                                <li class="flex items-start gap-3"><span class="text-[#F43F8A] mt-1">&#10003;</span> <strong>الشراكة الاستراتيجية:</strong> للشركات والمؤسسات الراغبة في دعم برامجنا بشكل مستمر.</li>
                                <li class="flex items-start gap-3"><span class="text-[#F43F8A] mt-1">&#10003;</span> <strong>رعاية الفعاليات:</strong> دعم ورش العمل والمسابقات التي ننظمها للشباب.</li>
                                <li class="flex items-start gap-3"><span class="text-[#F43F8A] mt-1">&#10003;</span> <strong>الشراكة الأكاديمية:</strong> للجامعات والمعاهد لتوفير فرص تدريبية للطلاب.</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <section class="bg-gradient-to-r from-[#F43F8A] to-[#4A90E2] text-white py-20">
                    <div class="container mx-auto px-6 text-center">
                        <h2 class="text-3xl md:text-4xl font-bold mb-4">هل أنت مستعد لبدء الشراكة؟</h2>
                        <p class="text-lg max-w-2xl mx-auto mb-8 text-gray-200">تواصل معنا اليوم لمناقشة كيف يمكننا التعاون لتحقيق أهداف مشتركة وخلق تأثير إيجابي دائم.</p>
                        <a href="#/contact" class="bg-white text-[#10062E] font-bold py-3 px-8 rounded-full hover:bg-gray-200 transition duration-300 text-lg shadow-2xl">تواصل معنا الآن</a>
                    </div>
                </section>
            </main>
    
            <!-- ======== صفحة "تواصل معنا" ======== -->
            <main id="contact-page" class="hidden-page">
                <div class="bg-[#140A38] py-20">
                    <div class="container mx-auto px-6 text-center">
                        <h1 class="text-4xl md:text-5xl font-extrabold brand-gradient-text mb-4">تواصل معنا</h1>
                        <p class="text-lg md:text-xl max-w-3xl mx-auto text-gray-300">نسعد دائمًا بسماع آرائكم واقتراحاتكم. استخدم النموذج التالي لإرسال رسالتك مباشرة إلينا.</p>
                    </div>
                </div>
                <div class="py-20 bg-[#10062E]">
                    <div class="container mx-auto px-6 max-w-2xl">
                        <div id="message-feedback" class="hidden-page px-4 py-3 rounded-lg relative mb-6 text-center" role="alert">
                            <strong class="font-bold"></strong>
                            <span class="block sm:inline"></span>
                        </div>
                        <form id="contact-form" class="bg-[#140A38] p-8 rounded-lg shadow-2xl shadow-purple-500/10">
                            <div class="mb-6"><label for="contact-name" class="block text-gray-300 mb-2 font-semibold">الاسم الكامل</label><input type="text" id="contact-name" required class="w-full p-3 bg-[#10062E] border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-pink-500 focus:outline-none"></div>
                            <div class="mb-6"><label class="block text-gray-300 mb-2 font-semibold">طريقة التواصل المفضلة</label><div class="flex items-center gap-x-8"><label class="flex items-center gap-2 cursor-pointer"><input type="radio" name="contact-method" value="email" checked class="form-radio bg-gray-700 text-pink-500 focus:ring-pink-500"><span class="text-white">البريد الإلكتروني</span></label><label class="flex items-center gap-2 cursor-pointer"><input type="radio" name="contact-method" value="phone" class="form-radio bg-gray-700 text-pink-500 focus:ring-pink-500"><span class="text-white">رقم الهاتف</span></label></div></div>
                            <div id="email-input-container" class="mb-6"><label for="contact-email" class="block text-gray-300 mb-2 font-semibold">البريد الإلكتروني</label><input type="email" id="contact-email" required class="w-full p-3 bg-[#10062E] border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-pink-500 focus:outline-none"></div>
                            <div id="phone-input-container" class="hidden-page mb-6"><label for="contact-phone" class="block text-gray-300 mb-2 font-semibold">رقم الهاتف</label><input type="tel" id="contact-phone" class="w-full p-3 bg-[#10062E] border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-pink-500 focus:outline-none"></div>
                            <div class="mb-6"><label for="contact-message" class="block text-gray-300 mb-2 font-semibold">رسالتك</label><textarea id="contact-message" rows="5" required class="w-full p-3 bg-[#10062E] border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-pink-500 focus:outline-none"></textarea></div>
                            <button type="submit" id="submit-button" class="w-full bg-[#F43F8A] text-white font-bold py-3 px-6 rounded-full hover:bg-pink-500 transition duration-300">إرسال الرسالة</button>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    
        <!-- ======== التذييل ======== -->
        <footer class="bg-black/20 text-white">
            <div class="container mx-auto px-6 py-12">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center md:text-right">
                    <div><h3 class="font-bold text-lg mb-4">آفاق الشباب</h3><p class="text-gray-400">تمكين الشباب الليبي لبناء مستقبل مشرق ومجتمعات مزدهرة.</p></div>
                    <div><h3 class="font-bold text-lg mb-4">روابط سريعة</h3><ul class="space-y-2"><li><a href="#/about" class="text-gray-400 hover:text-white">من نحن</a></li><li><a href="#/contact" class="text-gray-400 hover:text-white">تواصل معنا</a></li><li><a href="#/about#faq" class="text-gray-400 hover:text-white">الأسئلة الشائعة</a></li></ul></div>
                    <div><h3 class="font-bold text-lg mb-4">انضم إلينا</h3><ul class="space-y-2"><li><a href="#" class="text-gray-400 hover:text-white">كن متطوعاً</a></li><li><a href="#/partner" class="text-gray-400 hover:text-white">كن شريكاً</a></li></ul></div>
                    <div class="text-center md:text-right">
                        <h3 class="font-bold text-lg mb-4">تابعنا</h3>
                        <div class="mt-2">
                            <a href="#" class="inline-block mx-2 text-gray-400 hover:text-white"><svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M11.999 0C5.372 0 0 5.373 0 12s5.372 12 11.999 12C18.626 24 24 18.627 24 12S18.626 0 11.999 0ZM17.37 8.133l-2.458 11.542c-.285 1.334-1.025 1.667-2.132 1.042l-3.59-2.642-1.732 1.666c-.19.19-.357.357-.714.357l.25-3.66L6.03 10.25c-1.333-.82-1.333-2.15.286-2.875L15.98 4.62c1.143-.536 2.15.25 1.39 1.833-.002.002-.002.002 0 0Z"/></svg></a>
                            <a href="#" class="inline-block mx-2 text-gray-400 hover:text-white"><svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg></a>
                            <a href="#" class="inline-block mx-2 text-gray-400 hover:text-white"><svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-1.06-.63-1.9-1.48-2.5-2.55C.44 16.96.22 15.53.23 14.1v-2.11c.11-1.14.52-2.25 1.13-3.25.61-1.02 1.4-1.9 2.34-2.66.93-.76 2-1.28 3.15-1.49v4.66c-.94.3-1.82.72-2.62 1.25-.37.24-.7.55-1.01.91-.02.02-.03.04-.05.06-.21.22-.42.46-.6.71-.23.3-.41.64-.55.99-.07.15-.14.3-.2.46-.05.15-.08.3-.12.45-.02.09-.04.18-.05.28v.1c-.01.07-.01.14-.02.21v.41c.04 1.09.45 2.16 1.16 2.99.71.83 1.66 1.36 2.73 1.5.17.02.34.03.5.03.54 0 1.07-.08 1.59-.23.52-.15 1-.38 1.43-.69.43-.31.8-.7 1.11-1.14.31-.44.55-.93.7-1.46.02-.06.03-.12.04-.18.01-.05.02-.1.03-.15.01-.06.02-.12.02-.18v-11.5z"/></svg></a>
                            <a href="#" class="inline-block mx-2 text-gray-400 hover:text-white"><svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/></svg></a>
                        </div>
                    </div>
                </div>
                <div class="mt-12 border-t border-gray-700/50 pt-8 text-center text-gray-500"><p>&copy; 2025 آفاق الشباب. جميع الحقوق محفوظة.</p></div>
            </div>
        </footer>
    </div>

    <!-- Firebase SDKs -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInWithEmailAndPassword, onAuthStateChanged, signOut } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, setDoc, updateDoc } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        // Firebase configuration from your project
        const firebaseConfig = {
          apiKey: "AIzaSyC_mvpDvFmnelvvWsEg9jjeYQwRQZ5o5As",
          authDomain: "youth-horizons.firebaseapp.com",
          projectId: "youth-horizons",
          storageBucket: "youth-horizons.firebasestorage.app",
          messagingSenderId: "128462606912",
          appId: "1:128462606912:web:68a8f608cefa24556e45a5",
          measurementId: "G-8FSZHJ9WHK"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // العناصر
        const loadingPage = document.getElementById('loading-page');
        const loginPage = document.getElementById('login-page');
        const contentPage = document.getElementById('content-page');
        const loginForm = document.getElementById('login-form');
        const logoutButton = document.getElementById('logout-button');
        const messageFeedback = document.getElementById('message-feedback');

        // وظيفة لإنشاء معرف فريد للجهاز
        function getDeviceID() {
            let deviceID = localStorage.getItem('deviceID');
            if (!deviceID) {
                deviceID = crypto.randomUUID();
                localStorage.setItem('deviceID', deviceID);
            }
            return deviceID;
        }

        // وظيفة لإظهار رسالة
        function showMessage(message, type = 'error') {
            const strongTag = messageFeedback.querySelector('strong');
            const spanTag = messageFeedback.querySelector('span');
            if (type === 'error') {
                messageFeedback.className = "bg-red-500/20 border border-red-500 text-red-300 px-4 py-3 rounded-lg relative mb-6 text-center";
                strongTag.textContent = "خطأ!";
            } else {
                messageFeedback.className = "bg-green-500/20 border border-green-500 text-green-300 px-4 py-3 rounded-lg relative mb-6 text-center";
                strongTag.textContent = "نجاح!";
            }
            spanTag.textContent = message;
            messageFeedback.classList.remove('hidden');
        }

        // وظيفة للتحقق من صلاحية الوصول
        const checkAccess = async (user) => {
            const currentDeviceID = getDeviceID();
            const userDocRef = doc(db, "users", user.uid);
            
            try {
                const userDocSnap = await getDoc(userDocRef);

                if (!userDocSnap.exists()) {
                    // المستخدم الجديد يقوم بتسجيل الدخول لأول مرة
                    await setDoc(userDocRef, {
                        deviceID: currentDeviceID,
                        lastLogin: new Date()
                    });
                    console.log("✅ مستخدم جديد مسجل، تم ربط الجهاز.");
                    return true;
                } else {
                    // المستخدم موجود، تحقق من معرف الجهاز
                    const storedDeviceID = userDocSnap.data().deviceID;
                    if (storedDeviceID === currentDeviceID) {
                        console.log("✅ تم التحقق من الجهاز بنجاح.");
                        return true;
                    } else {
                        console.warn("⚠️ محاولة تسجيل دخول من جهاز غير مصرح به. سيتم تحديث الجهاز.");
                        await updateDoc(userDocRef, {
                            deviceID: currentDeviceID,
                            lastLogin: new Date()
                        });
                        return true; // السماح بالدخول بعد التحديث
                    }
                }
            } catch (error) {
                console.error("❌ خطأ في التحقق من الوصول:", error);
                return false;
            }
        };

        // وظيفة لإدارة واجهة المستخدم بناءً على حالة المستخدم والجهاز
        const manageUI = async (user) => {
            loadingPage.classList.add('hidden');
            if (user) {
                const hasAccess = await checkAccess(user);
                if (hasAccess) {
                    contentPage.style.display = 'block';
                    loginPage.classList.add('hidden');
                } else {
                    // تسجيل خروج المستخدم تلقائياً إذا كان الجهاز غير مصرح به
                    await signOut(auth);
                    loginPage.classList.remove('hidden');
                    showMessage("هذا الجهاز غير مصرح له بالدخول. يرجى تسجيل الدخول مرة أخرى.", 'error');
                }
            } else {
                loginPage.classList.remove('hidden');
                contentPage.style.display = 'none';
            }
        };

        // الاستماع لحالة المصادقة
        onAuthStateChanged(auth, (user) => {
            console.log("🔔 حالة المصادقة تغيرت:", user ? "مستخدم مسجل" : "لا يوجد مستخدم");
            manageUI(user);
        });

        // التعامل مع نموذج تسجيل الدخول
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = loginForm.email.value;
            const password = loginForm.password.value;
            
            try {
                const userCredential = await signInWithEmailAndPassword(auth, email, password);
                console.log("✅ تم تسجيل الدخول بنجاح!");
                showMessage("تم تسجيل الدخول بنجاح! جارِ فحص الجهاز...", 'success');
                // manageUI سيتم استدعاؤها تلقائياً بواسطة onAuthStateChanged
            } catch (error) {
                console.error("❌ خطأ في تسجيل الدخول:", error);
                showMessage("فشل تسجيل الدخول. يرجى التحقق من بياناتك.", 'error');
            }
        });

        // التعامل مع زر تسجيل الخروج
        logoutButton.addEventListener('click', async () => {
            try {
                await signOut(auth);
                console.log("✅ تم تسجيل الخروج بنجاح.");
                localStorage.removeItem('deviceID'); // حذف معرف الجهاز عند الخروج
                showMessage("تم تسجيل الخروج. يرجى تسجيل الدخول مرة أخرى.", 'success');
                // manageUI سيتم استدعاؤها تلقائياً بواسطة onAuthStateChanged
            } catch (error) {
                console.error("❌ خطأ في تسجيل الخروج:", error);
                showMessage("فشل تسجيل الخروج.", 'error');
            }
        });
    </script>

    <!-- كود التنقل بين الصفحات بدون التأثير على الأمان -->
    <script>
    // تعريف الصفحات
    const pages = {
        '#/': document.getElementById('home-page'),
        '#/about': document.getElementById('about-page'),
        '#/partner': document.getElementById('partner-page'),
        '#/contact': document.getElementById('contact-page')
    };

    function showPage(hash) {
        // إخفاء جميع الصفحات
        Object.values(pages).forEach(page => {
            if (page) page.classList.add('hidden-page');
        });
        // إظهار الصفحة المطلوبة
        if (pages[hash]) {
            pages[hash].classList.remove('hidden-page');
        } else {
            // إذا لم يوجد هاش، إظهار الرئيسية
            pages['#/'].classList.remove('hidden-page');
        }
    }

    // عند تغيير الـ hash
    window.addEventListener('hashchange', () => {
        showPage(location.hash);
    });

    // عند تحميل الصفحة أول مرة
    window.addEventListener('DOMContentLoaded', () => {
        showPage(location.hash || '#/');
    });
    </script>

</body>
</html>
